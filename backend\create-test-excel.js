const XLSX = require('xlsx');

// 创建测试数据
const testData = [
  {
    '方剂名称': '补中益气汤',
    '主成分': '黄芪,人参,白术,甘草',
    '组成': '黄芪18g,人参9g,白术9g,甘草6g,当归3g,陈皮6g,升麻3g,柴胡3g',
    '别名': '补中益气丸',
    '排除成分': '孕妇慎用',
    '文献来源': '脾胃论',
    '备注': '补中益气，升阳举陷'
  },
  {
    '方剂名称': '八珍汤',
    '主成分': '人参,白术,茯苓,甘草,当归,川芎,白芍,熟地黄',
    '组成': '人参9g,白术9g,茯苓9g,甘草6g,当归9g,川芎6g,白芍9g,熟地黄12g',
    '别名': '八珍丸',
    '排除成分': '',
    '文献来源': '正体类要',
    '备注': '气血双补'
  },
  {
    '方剂名称': '十全大补汤',
    '主成分': '人参,白术,茯苓,甘草,当归,川芎,白芍,熟地黄,黄芪,肉桂',
    '组成': '人参9g,白术9g,茯苓9g,甘草6g,当归9g,川芎6g,白芍9g,熟地黄12g,黄芪12g,肉桂3g',
    '别名': '十全大补丸',
    '排除成分': '阴虚火旺者慎用',
    '文献来源': '太平惠民和剂局方',
    '备注': '温补气血'
  }
];

// 创建工作簿
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.json_to_sheet(testData);

// 设置列宽
const colWidths = [
  { wch: 20 }, // 方剂名称
  { wch: 30 }, // 主成分
  { wch: 50 }, // 组成
  { wch: 15 }, // 别名
  { wch: 20 }, // 排除成分
  { wch: 20 }, // 文献来源
  { wch: 25 }  // 备注
];
worksheet['!cols'] = colWidths;

// 添加工作表到工作簿
XLSX.utils.book_append_sheet(workbook, worksheet, '中药药方数据');

// 写入文件
XLSX.writeFile(workbook, '../test-import.xlsx');

console.log('测试Excel文件已创建: test-import.xlsx');
