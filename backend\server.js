const express = require('express')
const cors = require('cors')
const bodyParser = require('body-parser')
const path = require('path')
const fs = require('fs').promises
const { v4: uuidv4 } = require('uuid')
const XLSX = require('xlsx')
const multer = require('multer')

const app = express()
const PORT = process.env.PORT || 3001

// 中间件
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://ev.zhcv.cn',
    'https://ev.zhcv.cn',
    'http://***************'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}))

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`)
  console.log('Request body:', req.body)
  next()
})

app.use(bodyParser.json())
app.use(bodyParser.urlencoded({ extended: true }))

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB限制
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.mimetype === 'application/vnd.ms-excel') {
      cb(null, true)
    } else {
      cb(new Error('只支持Excel文件格式'), false)
    }
  }
})

// 数据文件路径
const DATA_FILE = path.join(__dirname, 'data', 'wechat-accounts.json')
const ADMIN_FILE = path.join(__dirname, 'data', 'admin-accounts.json')
const KEYS_FILE = path.join(__dirname, 'data', 'access-keys.json')
const MEDICINE_FILE = path.join(__dirname, 'data', 'medicine-data.json')
const FORMULAS_FILE = path.join(__dirname, 'data', 'medicine-formulas.json')
const USER_LOGINS_FILE = path.join(__dirname, 'data', 'user-logins.json')

// 确保数据目录存在
async function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE)
  try {
    await fs.access(dataDir)
  } catch (error) {
    await fs.mkdir(dataDir, { recursive: true })
  }

  // 确保所有数据文件都有初始结构
  try {
    await readDataFile()
  } catch (error) {
    await writeDataFile({ wechatAccounts: [] })
  }

  try {
    await readAdminFile()
  } catch (error) {
    await writeAdminFile({
      admins: [
        {
          id: 'super-admin-001',
          username: 'admin',
          password: 'ww112233',
          role: 'super_admin',
          name: '超级管理员',
          createTime: new Date().toISOString(),
          lastLoginTime: null
        }
      ]
    })
  }

  try {
    await readKeysFile()
  } catch (error) {
    await writeKeysFile({ accessKeys: [] })
  }

  try {
    await readMedicineFile()
  } catch (error) {
    await writeMedicineFile({ medicines: [] })
  }

  try {
    await readFormulasFile()
  } catch (error) {
    await writeFormulasFile({ formulas: [] })
  }

  try {
    await readUserLoginsFile()
  } catch (error) {
    await writeUserLoginsFile({ userLogins: [] })
  }
}

// 读取数据文件
async function readDataFile() {
  try {
    const data = await fs.readFile(DATA_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构
    return { wechatAccounts: [] }
  }
}

// 写入数据文件
async function writeDataFile(data) {
  await fs.writeFile(DATA_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 读取管理员数据文件
async function readAdminFile() {
  try {
    const data = await fs.readFile(ADMIN_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构（包含超级管理员）
    return {
      admins: [
        {
          id: 'super-admin-001',
          username: 'admin',
          password: 'ww112233',
          role: 'super_admin',
          name: '超级管理员',
          createTime: new Date().toISOString(),
          lastLoginTime: null
        }
      ]
    }
  }
}

// 写入管理员数据文件
async function writeAdminFile(data) {
  await fs.writeFile(ADMIN_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 读取密钥数据文件
async function readKeysFile() {
  try {
    const data = await fs.readFile(KEYS_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构
    return { accessKeys: [] }
  }
}

// 写入密钥数据文件
async function writeKeysFile(data) {
  await fs.writeFile(KEYS_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 读取中药数据文件
async function readMedicineFile() {
  try {
    const data = await fs.readFile(MEDICINE_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构
    return { medicines: [] }
  }
}

// 写入中药数据文件
async function writeMedicineFile(data) {
  await fs.writeFile(MEDICINE_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 读取药方数据文件
async function readFormulasFile() {
  try {
    const data = await fs.readFile(FORMULAS_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构
    return { formulas: [] }
  }
}

// 写入药方数据文件
async function writeFormulasFile(data) {
  await fs.writeFile(FORMULAS_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 读取用户登录记录文件
async function readUserLoginsFile() {
  try {
    const data = await fs.readFile(USER_LOGINS_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // 如果文件不存在，返回默认结构
    return { userLogins: [] }
  }
}

// 写入用户登录记录文件
async function writeUserLoginsFile(data) {
  await fs.writeFile(USER_LOGINS_FILE, JSON.stringify(data, null, 2), 'utf8')
}

// 生成随机密钥
function generateAccessKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// API路由

// 获取客户端真实IP地址的函数
function getClientIP(req) {
  return req.headers['x-forwarded-for'] ||
    req.headers['x-real-ip'] ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
    req.ip ||
    '未知IP'
}

// 提交微信号
app.post('/api/wechat', async (req, res) => {
  try {
    const { wechatId } = req.body

    if (!wechatId || !wechatId.trim()) {
      return res.status(400).json({
        success: false,
        message: '微信号不能为空'
      })
    }

    // 验证微信号格式
    const trimmedWechatId = wechatId.trim()

    // 获取客户端IP地址
    const clientIP = getClientIP(req)
    console.log(`收到微信号提交请求 - IP: ${clientIP}, 微信号: ${trimmedWechatId}`)

    // 读取现有数据
    const data = await readDataFile()

    // 检查是否已存在相同微信号
    const existingAccount = data.wechatAccounts.find(
      account => account.wechatId === trimmedWechatId
    )

    if (existingAccount) {
      return res.status(400).json({
        success: false,
        message: '该微信号已存在'
      })
    }

    // 添加新记录（包含IP地址）
    const newAccount = {
      id: uuidv4(),
      wechatId: trimmedWechatId,
      submitTime: new Date().toISOString(),
      ipAddress: clientIP
    }

    data.wechatAccounts.push(newAccount)

    // 保存数据
    await writeDataFile(data)

    res.json({
      success: true,
      message: '微信号提交成功',
      data: newAccount
    })

  } catch (error) {
    console.error('提交微信号错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取所有微信号（管理员）
app.get('/api/wechat', async (req, res) => {
  try {
    const data = await readDataFile()

    // 按提交时间倒序排列
    data.wechatAccounts.sort((a, b) => new Date(b.submitTime) - new Date(a.submitTime))

    res.json({
      success: true,
      wechatAccounts: data.wechatAccounts
    })

  } catch (error) {
    console.error('获取微信号列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除微信号（管理员）
app.delete('/api/wechat/:id', async (req, res) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '微信号ID不能为空'
      })
    }

    // 读取现有数据
    const data = await readDataFile()

    // 查找要删除的记录
    const accountIndex = data.wechatAccounts.findIndex(
      account => account.id === id
    )

    if (accountIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的微信号记录'
      })
    }

    // 获取被删除的记录信息
    const deletedAccount = data.wechatAccounts[accountIndex]

    // 从数组中删除记录
    data.wechatAccounts.splice(accountIndex, 1)

    // 保存数据
    await writeDataFile(data)

    console.log(`删除微信号成功: ${deletedAccount.wechatId} (ID: ${id})`)

    res.json({
      success: true,
      message: '微信号删除成功',
      data: deletedAccount
    })

  } catch (error) {
    console.error('删除微信号错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 管理员登录验证
app.post('/api/admin/login', async (req, res) => {
  try {
    const { username, password } = req.body

    console.log('管理员登录尝试:', { username, password: password ? '***' : 'empty' })

    // 验证请求数据
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      })
    }

    // 从数据库验证管理员账号
    const adminData = await readAdminFile()
    const admin = adminData.admins.find(a => a.username === username.trim() && a.password === password.trim())

    if (admin) {
      // 更新最后登录时间
      admin.lastLoginTime = new Date().toISOString()
      await writeAdminFile(adminData)

      console.log('管理员登录成功:', admin.username)
      res.json({
        success: true,
        message: '登录成功',
        admin: {
          id: admin.id,
          username: admin.username,
          role: admin.role,
          name: admin.name
        }
      })
    } else {
      console.log('管理员登录失败: 用户名或密码错误')
      res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      })
    }
  } catch (error) {
    console.error('管理员登录错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString()
  })
})

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    success: false,
    message: '服务器内部错误'
  })
})

// 404处理 - 移动到所有路由定义之后

// 用户登录验证
app.post('/api/user/login', async (req, res) => {
  try {
    const { phoneNumber, accessKey } = req.body

    console.log('用户登录尝试:', { phoneNumber, accessKey: accessKey ? '***' : 'empty' })

    // 验证请求数据
    if (!phoneNumber || !accessKey) {
      return res.status(400).json({
        success: false,
        message: '手机号和密钥不能为空'
      })
    }

    // 验证密钥是否有效
    const keysData = await readKeysFile()
    const validKey = keysData.accessKeys.find(key =>
      key.keyValue === accessKey.trim() &&
      key.status === 'active' &&
      (key.expireTime === null || new Date(key.expireTime) > new Date())
    )

    if (!validKey) {
      console.log('用户登录失败: 密钥无效或已过期')
      return res.status(401).json({
        success: false,
        message: '密钥无效或已过期'
      })
    }

    // 更新密钥使用次数
    validKey.usageCount = (validKey.usageCount || 0) + 1
    validKey.lastUsedTime = new Date().toISOString()
    await writeKeysFile(keysData)

    // 获取客户端IP地址
    const clientIP = getClientIP(req)

    // 记录用户登录信息
    const userLoginsData = await readUserLoginsFile()

    // 检查是否为首次登录（注册）
    const existingLogins = userLoginsData.userLogins.filter(
      login => login.phoneNumber === phoneNumber.trim()
    )
    const isFirstLogin = existingLogins.length === 0

    const loginRecord = {
      id: uuidv4(),
      phoneNumber: phoneNumber.trim(),
      keyId: validKey.id,
      keyValue: validKey.keyValue,
      keyDescription: validKey.description || '',
      loginTime: new Date().toISOString(),
      ipAddress: clientIP,
      loginType: isFirstLogin ? 'register' : 'login', // 区分注册和登录
      loginCount: existingLogins.length + 1 // 登录次数
    }

    userLoginsData.userLogins.push(loginRecord)
    await writeUserLoginsFile(userLoginsData)

    console.log('用户登录成功:', phoneNumber, 'IP:', clientIP)
    res.json({
      success: true,
      message: '登录成功',
      user: {
        phoneNumber: phoneNumber.trim(),
        keyId: validKey.id,
        loginTime: new Date().toISOString()
      }
    })
  } catch (error) {
    console.error('用户登录错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取用户登录记录（管理员）
app.get('/api/user-logins', async (req, res) => {
  try {
    const userLoginsData = await readUserLoginsFile()

    // 按登录时间倒序排列
    userLoginsData.userLogins.sort((a, b) => new Date(b.loginTime) - new Date(a.loginTime))

    res.json({
      success: true,
      userLogins: userLoginsData.userLogins
    })

  } catch (error) {
    console.error('获取用户登录记录错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除用户登录记录（管理员）
app.delete('/api/user-logins/:id', async (req, res) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '记录ID不能为空'
      })
    }

    // 读取现有数据
    const userLoginsData = await readUserLoginsFile()

    // 查找要删除的记录
    const recordIndex = userLoginsData.userLogins.findIndex(
      record => record.id === id
    )

    if (recordIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的登录记录'
      })
    }

    // 获取被删除的记录信息
    const deletedRecord = userLoginsData.userLogins[recordIndex]

    // 从数组中删除记录
    userLoginsData.userLogins.splice(recordIndex, 1)

    // 保存数据
    await writeUserLoginsFile(userLoginsData)

    console.log(`删除用户登录记录成功: ${deletedRecord.phoneNumber} (ID: ${id})`)

    res.json({
      success: true,
      message: '登录记录删除成功',
      data: deletedRecord
    })

  } catch (error) {
    console.error('删除用户登录记录错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取管理员列表
app.get('/api/admin/users', async (req, res) => {
  try {
    const adminData = await readAdminFile()

    // 不返回密码信息
    const admins = adminData.admins.map(admin => ({
      id: admin.id,
      username: admin.username,
      role: admin.role,
      name: admin.name,
      createTime: admin.createTime,
      lastLoginTime: admin.lastLoginTime
    }))

    res.json({
      success: true,
      admins: admins
    })
  } catch (error) {
    console.error('获取管理员列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 添加管理员
app.post('/api/admin/users', async (req, res) => {
  try {
    const { username, password, role, name } = req.body

    // 验证请求数据
    if (!username || !password || !role || !name) {
      return res.status(400).json({
        success: false,
        message: '所有字段都不能为空'
      })
    }

    // 验证角色
    if (!['super_admin', 'admin', 'user'].includes(role)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色类型'
      })
    }

    const adminData = await readAdminFile()

    // 检查用户名是否已存在
    if (adminData.admins.find(admin => admin.username === username.trim())) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      })
    }

    // 生成新的管理员ID
    const newAdmin = {
      id: `admin-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      username: username.trim(),
      password: password.trim(),
      role: role,
      name: name.trim(),
      createTime: new Date().toISOString(),
      lastLoginTime: null
    }

    adminData.admins.push(newAdmin)
    await writeAdminFile(adminData)

    console.log('新增管理员:', newAdmin.username)
    res.json({
      success: true,
      message: '管理员添加成功',
      admin: {
        id: newAdmin.id,
        username: newAdmin.username,
        role: newAdmin.role,
        name: newAdmin.name,
        createTime: newAdmin.createTime
      }
    })
  } catch (error) {
    console.error('添加管理员错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 修改管理员密码
app.put('/api/admin/users/:id/password', async (req, res) => {
  try {
    const { id } = req.params
    const { newPassword } = req.body

    if (!newPassword) {
      return res.status(400).json({
        success: false,
        message: '新密码不能为空'
      })
    }

    const adminData = await readAdminFile()
    const adminIndex = adminData.admins.findIndex(admin => admin.id === id)

    if (adminIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在'
      })
    }

    adminData.admins[adminIndex].password = newPassword.trim()
    await writeAdminFile(adminData)

    console.log('管理员密码修改成功:', adminData.admins[adminIndex].username)
    res.json({
      success: true,
      message: '密码修改成功'
    })
  } catch (error) {
    console.error('修改密码错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除管理员
app.delete('/api/admin/users/:id', async (req, res) => {
  try {
    const { id } = req.params

    const adminData = await readAdminFile()
    const adminIndex = adminData.admins.findIndex(admin => admin.id === id)

    if (adminIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '管理员不存在'
      })
    }

    // 不允许删除超级管理员
    if (adminData.admins[adminIndex].role === 'super_admin') {
      return res.status(403).json({
        success: false,
        message: '不能删除超级管理员'
      })
    }

    const deletedAdmin = adminData.admins.splice(adminIndex, 1)[0]
    await writeAdminFile(adminData)

    console.log('删除管理员:', deletedAdmin.username)
    res.json({
      success: true,
      message: '管理员删除成功'
    })
  } catch (error) {
    console.error('删除管理员错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取密钥列表
app.get('/api/admin/keys', async (req, res) => {
  try {
    const keysData = await readKeysFile()

    res.json({
      success: true,
      keys: keysData.accessKeys
    })
  } catch (error) {
    console.error('获取密钥列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 生成新密钥
app.post('/api/admin/keys', async (req, res) => {
  try {
    const { type, description } = req.body

    // 验证请求数据
    if (!type) {
      return res.status(400).json({
        success: false,
        message: '密钥类型不能为空'
      })
    }

    // 验证密钥类型
    if (!['temporary', 'monthly', 'permanent'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '无效的密钥类型'
      })
    }

    const keysData = await readKeysFile()

    // 计算过期时间
    let expireTime = null
    if (type === 'temporary') {
      expireTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7天
    } else if (type === 'monthly') {
      expireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30天
    }

    // 生成新密钥
    const newKey = {
      id: `key-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      keyValue: generateAccessKey(),
      type: type,
      status: 'active',
      description: description?.trim() || '',
      usageCount: 0,
      createTime: new Date().toISOString(),
      expireTime: expireTime,
      lastUsedTime: null
    }

    keysData.accessKeys.push(newKey)
    await writeKeysFile(keysData)

    console.log('新增密钥:', newKey.keyValue)
    res.json({
      success: true,
      message: '密钥生成成功',
      key: newKey
    })
  } catch (error) {
    console.error('生成密钥错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 更新密钥状态
app.put('/api/admin/keys/:id/status', async (req, res) => {
  try {
    const { id } = req.params
    const { status } = req.body

    if (!status || !['active', 'disabled'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      })
    }

    const keysData = await readKeysFile()
    const keyIndex = keysData.accessKeys.findIndex(key => key.id === id)

    if (keyIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '密钥不存在'
      })
    }

    keysData.accessKeys[keyIndex].status = status
    await writeKeysFile(keysData)

    console.log('密钥状态更新:', keysData.accessKeys[keyIndex].keyValue, status)
    res.json({
      success: true,
      message: '状态更新成功'
    })
  } catch (error) {
    console.error('更新密钥状态错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除密钥
app.delete('/api/admin/keys/:id', async (req, res) => {
  try {
    const { id } = req.params

    const keysData = await readKeysFile()
    const keyIndex = keysData.accessKeys.findIndex(key => key.id === id)

    if (keyIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '密钥不存在'
      })
    }

    const deletedKey = keysData.accessKeys.splice(keyIndex, 1)[0]
    await writeKeysFile(keysData)

    console.log('删除密钥:', deletedKey.keyValue)
    res.json({
      success: true,
      message: '密钥删除成功'
    })
  } catch (error) {
    console.error('删除密钥错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 中药查询API
app.post('/api/medicine/search', async (req, res) => {
  try {
    const { keyword, type } = req.body

    if (!keyword || !keyword.trim()) {
      return res.status(400).json({
        success: false,
        message: '查询关键词不能为空'
      })
    }

    const medicineData = await readMedicineFile()
    let results = []

    // 模拟查询逻辑（实际项目中应该连接真实的中药数据库）
    const searchTerm = keyword.trim().toLowerCase()

    // 这里添加一些示例数据用于演示
    const sampleMedicines = [
      {
        id: '1',
        name: '人参',
        type: '补益药',
        effects: '大补元气，复脉固脱，补脾益肺，生津止渴，安神益智',
        indications: '体虚欲脱，肢冷脉微，脾虚食少，肺虚喘咳，津伤口渴，内热消渴，气血亏虚，久病虚羸，惊悸失眠，阳痿宫冷',
        dosage: '3-9g，另煎兑服',
        contraindications: '实证、热证忌服'
      },
      {
        id: '2',
        name: '当归',
        type: '补血药',
        effects: '补血活血，调经止痛，润肠通便',
        indications: '血虚萎黄，眩晕心悸，月经不调，经闭痛经，虚寒腹痛，风湿痹痛，跌扑损伤，痈疽疮疡，肠燥便秘',
        dosage: '6-12g',
        contraindications: '湿盛中满及大便溏泄者慎服'
      }
    ]

    // 简单的搜索逻辑
    results = sampleMedicines.filter(medicine => {
      if (type === 'name') {
        return medicine.name.toLowerCase().includes(searchTerm)
      } else if (type === 'effect') {
        return medicine.effects.toLowerCase().includes(searchTerm)
      } else if (type === 'symptom') {
        return medicine.indications.toLowerCase().includes(searchTerm)
      } else {
        return medicine.name.toLowerCase().includes(searchTerm) ||
          medicine.effects.toLowerCase().includes(searchTerm) ||
          medicine.indications.toLowerCase().includes(searchTerm)
      }
    })

    console.log('中药查询:', keyword, '结果数量:', results.length)
    res.json({
      success: true,
      medicines: results,
      total: results.length
    })
  } catch (error) {
    console.error('中药查询错误:', error)
    res.status(500).json({
      success: false,
      message: '查询服务暂时不可用'
    })
  }
})

// ==================== 中药药方管理API ====================

// 获取所有药方
app.get('/api/medicine/formulas', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', searchType = 'all' } = req.query
    const formulasData = await readFormulasFile()

    let filteredFormulas = formulasData.formulas

    // 搜索过滤
    if (search && search.trim()) {
      const searchTerm = search.trim().toLowerCase()
      filteredFormulas = formulasData.formulas.filter(formula => {
        switch (searchType) {
          case 'name':
            return formula.formula_name.toLowerCase().includes(searchTerm)
          case 'composition':
            return formula.composition_dosage && formula.composition_dosage.toLowerCase().includes(searchTerm)
          case 'initials':
            return formula.name_or_initials && formula.name_or_initials.toLowerCase().includes(searchTerm)
          case 'article':
            return formula.article_text && formula.article_text.toLowerCase().includes(searchTerm)
          case 'notes':
            return formula.selected_notes && formula.selected_notes.toLowerCase().includes(searchTerm)
          default:
            return formula.formula_name.toLowerCase().includes(searchTerm) ||
              (formula.composition_dosage && formula.composition_dosage.toLowerCase().includes(searchTerm)) ||
              (formula.name_or_initials && formula.name_or_initials.toLowerCase().includes(searchTerm)) ||
              (formula.article_text && formula.article_text.toLowerCase().includes(searchTerm)) ||
              (formula.selected_notes && formula.selected_notes.toLowerCase().includes(searchTerm))
        }
      })
    }

    // 分页
    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const startIndex = (pageNum - 1) * limitNum
    const endIndex = startIndex + limitNum
    const paginatedFormulas = filteredFormulas.slice(startIndex, endIndex)

    // 按创建时间倒序排列
    paginatedFormulas.sort((a, b) => new Date(b.create_time) - new Date(a.create_time))

    res.json({
      success: true,
      formulas: paginatedFormulas,
      pagination: {
        current: pageNum,
        pageSize: limitNum,
        total: filteredFormulas.length,
        totalPages: Math.ceil(filteredFormulas.length / limitNum)
      }
    })
  } catch (error) {
    console.error('获取药方列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 添加新药方
app.post('/api/medicine/formulas', async (req, res) => {
  try {
    const {
      formula_name,
      composition_dosage,
      name_or_initials,
      article_text,
      selected_notes
    } = req.body

    // 验证必填字段
    if (!formula_name || !formula_name.trim()) {
      return res.status(400).json({
        success: false,
        message: '方名不能为空'
      })
    }

    if (!composition_dosage || !composition_dosage.trim()) {
      return res.status(400).json({
        success: false,
        message: '组成剂量不能为空'
      })
    }

    const formulasData = await readFormulasFile()

    // 检查是否已存在相同名称的药方
    const existingFormula = formulasData.formulas.find(
      formula => formula.formula_name.trim().toLowerCase() === formula_name.trim().toLowerCase()
    )

    if (existingFormula) {
      return res.status(400).json({
        success: false,
        message: '该药方名称已存在'
      })
    }

    // 创建新药方记录
    const newFormula = {
      id: uuidv4(),
      formula_name: formula_name.trim(),
      composition_dosage: composition_dosage.trim(),
      name_or_initials: name_or_initials ? name_or_initials.trim() : '',
      article_text: article_text ? article_text.trim() : '',
      selected_notes: selected_notes ? selected_notes.trim() : '',
      create_time: new Date().toISOString(),
      update_time: new Date().toISOString()
    }

    formulasData.formulas.push(newFormula)
    await writeFormulasFile(formulasData)

    console.log('新增药方成功:', newFormula.formula_name)
    res.json({
      success: true,
      message: '药方添加成功',
      formula: newFormula
    })
  } catch (error) {
    console.error('添加药方错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 更新药方
app.put('/api/medicine/formulas/:id', async (req, res) => {
  try {
    const { id } = req.params
    const {
      formula_name,
      composition_dosage,
      name_or_initials,
      article_text,
      selected_notes
    } = req.body

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '药方ID不能为空'
      })
    }

    // 验证必填字段
    if (!formula_name || !formula_name.trim()) {
      return res.status(400).json({
        success: false,
        message: '方名不能为空'
      })
    }

    if (!composition_dosage || !composition_dosage.trim()) {
      return res.status(400).json({
        success: false,
        message: '组成剂量不能为空'
      })
    }

    const formulasData = await readFormulasFile()
    const formulaIndex = formulasData.formulas.findIndex(formula => formula.id === id)

    if (formulaIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的药方'
      })
    }

    // 检查是否与其他药方名称重复（排除自己）
    const existingFormula = formulasData.formulas.find(
      formula => formula.id !== id &&
        formula.formula_name.trim().toLowerCase() === formula_name.trim().toLowerCase()
    )

    if (existingFormula) {
      return res.status(400).json({
        success: false,
        message: '该药方名称已存在'
      })
    }

    // 更新药方信息
    const updatedFormula = {
      ...formulasData.formulas[formulaIndex],
      formula_name: formula_name.trim(),
      composition_dosage: composition_dosage.trim(),
      name_or_initials: name_or_initials ? name_or_initials.trim() : '',
      article_text: article_text ? article_text.trim() : '',
      selected_notes: selected_notes ? selected_notes.trim() : '',
      update_time: new Date().toISOString()
    }

    formulasData.formulas[formulaIndex] = updatedFormula
    await writeFormulasFile(formulasData)

    console.log('更新药方成功:', updatedFormula.formula_name)
    res.json({
      success: true,
      message: '药方更新成功',
      formula: updatedFormula
    })
  } catch (error) {
    console.error('更新药方错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除药方
app.delete('/api/medicine/formulas/:id', async (req, res) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '药方ID不能为空'
      })
    }

    const formulasData = await readFormulasFile()
    const formulaIndex = formulasData.formulas.findIndex(formula => formula.id === id)

    if (formulaIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的药方'
      })
    }

    const deletedFormula = formulasData.formulas[formulaIndex]
    formulasData.formulas.splice(formulaIndex, 1)
    await writeFormulasFile(formulasData)

    console.log('删除药方成功:', deletedFormula.formula_name)
    res.json({
      success: true,
      message: '药方删除成功',
      formula: deletedFormula
    })
  } catch (error) {
    console.error('删除药方错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// Excel导出药方数据
app.get('/api/medicine/formulas/export', async (req, res) => {
  try {
    const formulasData = await readFormulasFile()

    // 准备导出数据
    const exportData = formulasData.formulas.map((formula, index) => ({
      '方名': formula.formula_name,
      '组成剂量A': formula.composition_dosage || '',
      '方名或首字母': formula.name_or_initials || '',
      '条文': formula.article_text || '',
      '精选笔记': formula.selected_notes || ''
    }))

    // 创建工作簿
    const workbook = XLSX.utils.book_new()
    const worksheet = XLSX.utils.json_to_sheet(exportData)

    // 设置列宽
    const colWidths = [
      { wch: 30 }, // 方名
      { wch: 50 }, // 组成剂量A
      { wch: 40 }, // 方名或首字母
      { wch: 60 }, // 条文
      { wch: 25 }  // 精选笔记
    ]
    worksheet['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '中药药方数据')

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // 设置响应头
    const filename = `中药药方数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)

    // 发送Excel文件
    res.send(excelBuffer)
  } catch (error) {
    console.error('导出药方数据错误:', error)
    res.status(500).json({
      success: false,
      message: '导出失败'
    })
  }
})

// 批量导入药方数据
app.post('/api/medicine/formulas/import', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要导入的Excel文件'
      })
    }

    // 解析Excel文件
    const workbook = XLSX.read(req.file.buffer, { type: 'buffer' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 1 }) // 跳过第一行标题

    if (!jsonData || jsonData.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Excel文件中没有数据'
      })
    }

    const formulasData = await readFormulasFile()
    const importResults = {
      success: 0,
      failed: 0,
      errors: []
    }

    for (let i = 0; i < jsonData.length; i++) {
      try {
        const row = jsonData[i]
        const rowNum = i + 2 // Excel行号（从第2行开始，第1行是标题）

        // 验证必填字段
        if (!row['方名'] || !row['方名'].toString().trim()) {
          importResults.failed++
          importResults.errors.push(`第${rowNum}行: 方名不能为空`)
          continue
        }

        if (!row['组成剂量A'] || !row['组成剂量A'].toString().trim()) {
          importResults.failed++
          importResults.errors.push(`第${rowNum}行: 组成剂量A不能为空`)
          continue
        }

        const formulaName = row['方名'].toString().trim()

        // 检查是否已存在相同名称的药方
        const existingFormula = formulasData.formulas.find(
          formula => formula.formula_name.trim().toLowerCase() === formulaName.toLowerCase()
        )

        if (existingFormula) {
          importResults.failed++
          importResults.errors.push(`第${rowNum}行: 药方名称"${formulaName}"已存在`)
          continue
        }

        // 创建新药方记录
        const newFormula = {
          id: uuidv4(),
          formula_name: formulaName,
          composition_dosage: row['组成剂量A'].toString().trim(),
          name_or_initials: row['方名或首字母'] ? row['方名或首字母'].toString().trim() : '',
          article_text: row['条文'] ? row['条文'].toString().trim() : '',
          selected_notes: row['精选笔记'] ? row['精选笔记'].toString().trim() : '',
          create_time: new Date().toISOString(),
          update_time: new Date().toISOString()
        }

        formulasData.formulas.push(newFormula)
        importResults.success++
      } catch (error) {
        importResults.failed++
        importResults.errors.push(`第${i + 2}行: ${error.message}`)
      }
    }

    // 保存数据
    await writeFormulasFile(formulasData)

    console.log('批量导入药方完成:', importResults)
    res.json({
      success: true,
      message: `导入完成，成功${importResults.success}条，失败${importResults.failed}条`,
      results: importResults
    })
  } catch (error) {
    console.error('批量导入药方错误:', error)
    res.status(500).json({
      success: false,
      message: '导入失败: ' + error.message
    })
  }
})

// 获取单个药方详情
app.get('/api/medicine/formulas/:id', async (req, res) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '药方ID不能为空'
      })
    }

    const formulasData = await readFormulasFile()
    const formula = formulasData.formulas.find(formula => formula.id === id)

    if (!formula) {
      return res.status(404).json({
        success: false,
        message: '未找到指定的药方'
      })
    }

    res.json({
      success: true,
      formula: formula
    })
  } catch (error) {
    console.error('获取药方详情错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 404处理 - 必须在所有路由定义之后
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在'
  })
})

// 启动服务器
async function startServer() {
  try {
    await ensureDataDirectory()

    const server = app.listen(PORT, '0.0.0.0', () => {
      console.log('='.repeat(50))
      console.log('🚀 微信号收集系统后端服务启动成功!')
      console.log(`📡 服务器运行在: http://localhost:${PORT}`)
      console.log(`🏥 健康检查: http://localhost:${PORT}/api/health`)
      console.log(`📊 默认超级管理员: admin / ww112233`)
      console.log(`📁 数据文件: ${DATA_FILE}`)
      console.log('='.repeat(50))
    })

    // 优雅关闭
    process.on('SIGTERM', () => {
      console.log('收到SIGTERM信号，正在关闭服务器...')
      server.close(() => {
        console.log('服务器已关闭')
        process.exit(0)
      })
    })

    process.on('SIGINT', () => {
      console.log('收到SIGINT信号，正在关闭服务器...')
      server.close(() => {
        console.log('服务器已关闭')
        process.exit(0)
      })
    })

  } catch (error) {
    console.error('❌ 启动服务器失败:', error)
    process.exit(1)
  }
}

startServer()
