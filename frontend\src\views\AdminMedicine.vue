<template>
  <div class="admin-medicine">
    <!-- 顶部控制栏 -->
    <div class="control-bar">
      <div class="control-left">
        <h2 class="page-title">
          <span class="title-icon">🌿</span>
          中药药方管理
        </h2>
        <div class="stats-info">
          <span class="stats-item">
            <span class="stats-label">总计:</span>
            <span class="stats-value">{{ pagination.total }}</span>
          </span>
        </div>
      </div>
      <div class="control-right">
        <button class="action-btn add-btn" @click="showAddModal">
          <span class="btn-icon">➕</span>
          <span class="btn-text">添加药方</span>
        </button>
        <button class="action-btn import-btn" @click="openImportModal">
          <span class="btn-icon">📂</span>
          <span class="btn-text">批量导入</span>
        </button>
        <button class="action-btn export-btn" @click="exportData">
          <span class="btn-icon">💾</span>
          <span class="btn-text">数据导出</span>
        </button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <div class="search-input-group">
        <select v-model="searchType" class="search-type-select">
          <option value="all">全部</option>
          <option value="name">方名</option>
          <option value="composition">组成剂量</option>
          <option value="initials">方名或首字母</option>
          <option value="article">条文</option>
          <option value="notes">精选笔记</option>
        </select>
        <input
          v-model="searchKeyword"
          type="text"
          placeholder="输入搜索关键词..."
          class="search-input"
          @keyup.enter="searchFormulas"
        />
        <button class="search-btn" @click="searchFormulas">
          <span class="btn-icon">🔍</span>
          <span class="btn-text">搜索</span>
        </button>
        <button class="reset-btn" @click="resetSearch">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">重置</span>
        </button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <div class="table-wrapper">
        <table class="formulas-table">
          <thead>
            <tr>
              <th class="col-index">序号</th>
              <th class="col-name">方名</th>
              <th class="col-composition">组成剂量A</th>
              <th class="col-initials">方名或首字母</th>
              <th class="col-article">条文</th>
              <th class="col-notes">精选笔记</th>
              <th class="col-actions">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="loading" class="loading-row">
              <td colspan="7" class="loading-cell">
                <div class="loading-spinner">
                  <span class="spinner-icon">⏳</span>
                  <span class="loading-text">加载中...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="formulas.length === 0" class="empty-row">
              <td colspan="7" class="empty-cell">
                <div class="empty-state">
                  <span class="empty-icon">📝</span>
                  <span class="empty-text">暂无药方数据</span>
                </div>
              </td>
            </tr>
            <tr
              v-else
              v-for="(formula, index) in formulas"
              :key="formula.id"
              class="formula-row"
              :class="{ expanded: expandedRows.includes(formula.id) }"
            >
              <td class="col-index">
                {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
              </td>
              <td class="col-name">
                <div class="name-cell">
                  <span class="formula-name">{{ formula.formula_name }}</span>
                </div>
              </td>
              <td class="col-composition">
                <div class="composition-cell">
                  <span class="composition-text">{{
                    formula.composition_dosage || "-"
                  }}</span>
                </div>
              </td>
              <td class="col-initials">
                <div class="initials-cell">
                  <span class="initials-text">{{
                    formula.name_or_initials || "-"
                  }}</span>
                </div>
              </td>
              <td class="col-article">
                <div class="article-cell">
                  <span class="article-text">{{
                    formula.article_text && formula.article_text.length > 50
                      ? formula.article_text.substring(0, 50) + "..."
                      : formula.article_text || "-"
                  }}</span>
                </div>
              </td>
              <td class="col-notes">
                <div class="notes-cell">
                  <span class="notes-text">{{
                    formula.selected_notes || "-"
                  }}</span>
                </div>
              </td>

              <td class="col-actions">
                <div class="actions-cell">
                  <button
                    class="action-icon-btn detail-btn"
                    @click="toggleExpand(formula.id)"
                    :title="
                      expandedRows.includes(formula.id)
                        ? '收起详情'
                        : '展开详情'
                    "
                  >
                    <span class="btn-icon">{{
                      expandedRows.includes(formula.id) ? "📄" : "📋"
                    }}</span>
                  </button>
                  <button
                    class="action-icon-btn edit-btn"
                    @click="editFormula(formula)"
                    title="编辑"
                  >
                    <span class="btn-icon">📝</span>
                  </button>
                  <button
                    class="action-icon-btn delete-btn"
                    @click="deleteFormula(formula)"
                    title="删除"
                  >
                    <span class="btn-icon">🗑️</span>
                  </button>
                </div>
              </td>
            </tr>
            <!-- 展开的详情行 -->
            <tr
              v-for="formula in formulas"
              :key="`${formula.id}-detail`"
              v-show="expandedRows.includes(formula.id)"
              class="detail-row"
            >
              <td colspan="7" class="detail-cell">
                <div class="detail-content">
                  <div class="detail-section">
                    <h4 class="detail-title">完整条文</h4>
                    <div class="detail-text">
                      {{ formula.article_text || "暂无数据" }}
                    </div>
                  </div>
                  <div class="detail-section">
                    <h4 class="detail-title">精选笔记</h4>
                    <div class="detail-text">
                      {{ formula.selected_notes || "暂无数据" }}
                    </div>
                  </div>
                  <div class="detail-section">
                    <h4 class="detail-title">更新时间</h4>
                    <div class="detail-text">
                      {{ formatTime(formula.update_time) }}
                    </div>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container" v-if="formulas.length > 0">
      <div class="pagination-info">
        <span class="info-text">
          显示第 {{ (pagination.current - 1) * pagination.pageSize + 1 }} -
          {{
            Math.min(pagination.current * pagination.pageSize, pagination.total)
          }}
          条， 共 {{ pagination.total }} 条记录
        </span>
      </div>
      <div class="pagination-controls">
        <button
          class="page-btn prev-btn"
          :disabled="pagination.current <= 1"
          @click="changePage(pagination.current - 1)"
        >
          <span class="btn-icon">⬅️</span>
          <span class="btn-text">上一页</span>
        </button>
        <div class="page-numbers">
          <button
            v-for="page in getPageNumbers()"
            :key="page"
            class="page-number-btn"
            :class="{ active: page === pagination.current }"
            @click="changePage(page)"
            :disabled="page === '...'"
          >
            {{ page }}
          </button>
        </div>
        <button
          class="page-btn next-btn"
          :disabled="pagination.current >= pagination.totalPages"
          @click="changePage(pagination.current + 1)"
        >
          <span class="btn-text">下一页</span>
          <span class="btn-icon">➡️</span>
        </button>
      </div>
    </div>

    <!-- 添加/编辑药方弹窗 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <span class="modal-icon">{{ isEditing ? "📝" : "➕" }}</span>
            {{ isEditing ? "编辑药方" : "添加药方" }}
          </h3>
          <button class="modal-close-btn" @click="closeModal">
            <span class="close-icon">✖️</span>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveFormula" class="formula-form">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label"
                  >方名 <span class="required">*</span></label
                >
                <input
                  v-model="formulaForm.formula_name"
                  type="text"
                  class="form-input"
                  placeholder="请输入方名"
                  required
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label"
                  >组成剂量A <span class="required">*</span></label
                >
                <textarea
                  v-model="formulaForm.composition_dosage"
                  class="form-textarea"
                  placeholder="请输入组成剂量，如：知母24  芦根24  青竹茹24  粳米24"
                  rows="3"
                  required
                ></textarea>
                <div class="form-hint">请输入药材名称和剂量</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">方名或首字母</label>
                <input
                  v-model="formulaForm.name_or_initials"
                  type="text"
                  class="form-input"
                  placeholder="请输入方名或首字母缩写，如：千金知母四物解肌汤/QJZMSWJJT"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">条文</label>
                <textarea
                  v-model="formulaForm.article_text"
                  class="form-textarea"
                  placeholder="请输入条文内容"
                  rows="4"
                ></textarea>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">精选笔记</label>
                <textarea
                  v-model="formulaForm.selected_notes"
                  class="form-textarea"
                  placeholder="请输入精选笔记"
                  rows="3"
                ></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="modal-btn cancel-btn"
            @click="closeModal"
          >
            <span class="btn-icon">❌</span>
            <span class="btn-text">取消</span>
          </button>
          <button
            type="submit"
            class="modal-btn save-btn"
            @click="saveFormula"
            :disabled="saving"
          >
            <span class="btn-icon">{{ saving ? "⏳" : "💾" }}</span>
            <span class="btn-text">{{ saving ? "保存中..." : "保存" }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 导入弹窗 -->
    <div v-if="showImportModal" class="modal-overlay" @click="closeImportModal">
      <div class="modal-container import-modal" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
            <span class="modal-icon">📂</span>
            批量导入药方
          </h3>
          <button class="modal-close-btn" @click="closeImportModal">
            <span class="close-icon">✖️</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="import-instructions">
            <h4>导入说明：</h4>
            <ul>
              <li>支持Excel文件格式（.xlsx、.xls）</li>
              <li>
                第一行为标题行，包含：方名、组成剂量A、方名或首字母、条文、精选笔记
              </li>
              <li>方名和组成剂量A为必填字段</li>
              <li>系统会自动跳过第一行标题</li>
              <li>重复的药方名称将被跳过</li>
            </ul>
          </div>
          <div class="import-area">
            <input
              ref="fileInput"
              type="file"
              accept=".xlsx,.xls"
              @change="handleFileSelect"
              class="file-input"
              style="display: none"
            />
            <div class="file-drop-zone" @click="$refs.fileInput.click()">
              <div class="drop-content">
                <span class="drop-icon">📁</span>
                <span class="drop-text">点击选择Excel文件</span>
                <span class="drop-hint">支持 .xlsx、.xls 格式，最大10MB</span>
              </div>
            </div>
            <div v-if="importFile" class="selected-file">
              <span class="file-icon">📄</span>
              <span class="file-name">{{ importFile.name }}</span>
              <span class="file-size"
                >({{ formatFileSize(importFile.size) }})</span
              >
              <button class="remove-file-btn" @click="removeFile">
                <span class="remove-icon">❌</span>
              </button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="modal-btn cancel-btn"
            @click="closeImportModal"
          >
            <span class="btn-icon">❌</span>
            <span class="btn-text">取消</span>
          </button>
          <button
            type="button"
            class="modal-btn import-confirm-btn"
            @click="importData"
            :disabled="!importFile || importing"
          >
            <span class="btn-icon">{{ importing ? "⏳" : "📂" }}</span>
            <span class="btn-text">{{
              importing ? "导入中..." : "开始导入"
            }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AdminMedicine",
  data() {
    return {
      // 数据状态
      formulas: [],
      loading: false,

      // 分页状态
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0,
      },

      // 搜索状态
      searchKeyword: "",
      searchType: "all",

      // 展开状态
      expandedRows: [],

      // 弹窗状态
      showModal: false,
      showImportModal: false,
      showDeleteModal: false,
      isEditing: false,
      saving: false,
      importing: false,
      deleting: false,

      // 表单数据
      formulaForm: {
        id: null,
        formula_name: "",
        composition_dosage: "",
        name_or_initials: "",
        article_text: "",
        selected_notes: "",
      },

      // 导入相关
      importFile: null,

      // 删除相关
      deleteTarget: null,
    };
  },

  mounted() {
    console.log("中药药方管理页面已加载");
    this.loadFormulas();
  },

  methods: {
    // 加载药方数据
    async loadFormulas() {
      try {
        this.loading = true;
        const params = new URLSearchParams({
          page: this.pagination.current,
          limit: this.pagination.pageSize,
          search: this.searchKeyword,
          searchType: this.searchType,
        });

        const response = await fetch(`/api/medicine/formulas?${params}`);
        const result = await response.json();

        if (result.success) {
          this.formulas = result.formulas || [];
          this.pagination = {
            ...this.pagination,
            ...result.pagination,
          };
        } else {
          this.showMessage("加载失败: " + result.message, "error");
        }
      } catch (error) {
        console.error("加载药方数据错误:", error);
        this.showMessage("加载失败，请检查网络连接", "error");
      } finally {
        this.loading = false;
      }
    },

    // 格式化时间
    formatTime(timeString) {
      if (!timeString) return "-";
      return new Date(timeString).toLocaleString("zh-CN");
    },

    // 展开/收起详情
    toggleExpand(formulaId) {
      const index = this.expandedRows.indexOf(formulaId);
      if (index > -1) {
        this.expandedRows.splice(index, 1);
      } else {
        this.expandedRows.push(formulaId);
      }
    },

    // 搜索药方
    async searchFormulas() {
      this.pagination.current = 1;
      await this.loadFormulas();
    },

    // 重置搜索
    async resetSearch() {
      this.searchKeyword = "";
      this.searchType = "all";
      this.pagination.current = 1;
      await this.loadFormulas();
    },

    // 显示消息
    showMessage(message, type = "info") {
      // 简单的消息提示，可以后续替换为更好的UI组件
      alert(message);
    },

    // 切换页面
    async changePage(page) {
      if (page >= 1 && page <= this.pagination.totalPages) {
        this.pagination.current = page;
        await this.loadFormulas();
      }
    },

    // 获取页码数组
    getPageNumbers() {
      const current = this.pagination.current;
      const total = this.pagination.totalPages;
      const pages = [];

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i);
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i);
          }
          pages.push("...");
          pages.push(total);
        } else if (current >= total - 3) {
          pages.push(1);
          pages.push("...");
          for (let i = total - 4; i <= total; i++) {
            pages.push(i);
          }
        } else {
          pages.push(1);
          pages.push("...");
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i);
          }
          pages.push("...");
          pages.push(total);
        }
      }

      return pages;
    },

    // 导出数据
    async exportData() {
      try {
        const response = await fetch("/api/medicine/formulas/export");

        if (response.ok) {
          // 获取文件名
          const contentDisposition = response.headers.get(
            "Content-Disposition"
          );
          let filename = `中药药方数据_${new Date()
            .toISOString()
            .slice(0, 10)}.xlsx`;
          if (contentDisposition) {
            const filenameMatch = contentDisposition.match(
              /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
            );
            if (filenameMatch) {
              filename = decodeURIComponent(
                filenameMatch[1].replace(/['"]/g, "")
              );
            }
          }

          // 获取文件数据
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);

          const link = document.createElement("a");
          link.href = url;
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(url);

          this.showMessage("导出成功！", "success");
        } else {
          const result = await response.json();
          this.showMessage("导出失败: " + result.message, "error");
        }
      } catch (error) {
        console.error("导出数据错误:", error);
        this.showMessage("导出失败，请检查网络连接", "error");
      }
    },

    // 删除药方
    async deleteFormula(formula) {
      if (
        !confirm(`确定要删除药方"${formula.formula_name}"吗？此操作不可撤销！`)
      ) {
        return;
      }

      try {
        const response = await fetch(`/api/medicine/formulas/${formula.id}`, {
          method: "DELETE",
        });
        const result = await response.json();

        if (result.success) {
          this.showMessage("删除成功！", "success");
          await this.loadFormulas();
        } else {
          this.showMessage("删除失败: " + result.message, "error");
        }
      } catch (error) {
        console.error("删除药方错误:", error);
        this.showMessage("删除失败，请检查网络连接", "error");
      }
    },

    // 显示添加弹窗
    showAddModal() {
      this.isEditing = false;
      this.resetForm();
      this.showModal = true;
    },

    // 编辑药方
    editFormula(formula) {
      this.isEditing = true;
      this.formulaForm = {
        id: formula.id,
        formula_name: formula.formula_name,
        composition_dosage: formula.composition_dosage || "",
        name_or_initials: formula.name_or_initials || "",
        article_text: formula.article_text || "",
        selected_notes: formula.selected_notes || "",
      };
      this.showModal = true;
    },

    // 重置表单
    resetForm() {
      this.formulaForm = {
        id: null,
        formula_name: "",
        composition_dosage: "",
        name_or_initials: "",
        article_text: "",
        selected_notes: "",
      };
    },

    // 关闭弹窗
    closeModal() {
      this.showModal = false;
      this.resetForm();
    },

    // 保存药方
    async saveFormula() {
      if (!this.formulaForm.formula_name.trim()) {
        this.showMessage("方名不能为空", "error");
        return;
      }

      if (!this.formulaForm.composition_dosage.trim()) {
        this.showMessage("组成剂量不能为空", "error");
        return;
      }

      try {
        this.saving = true;

        // 处理表单数据
        const formData = {
          formula_name: this.formulaForm.formula_name.trim(),
          composition_dosage: this.formulaForm.composition_dosage.trim(),
          name_or_initials: this.formulaForm.name_or_initials.trim(),
          article_text: this.formulaForm.article_text.trim(),
          selected_notes: this.formulaForm.selected_notes.trim(),
        };

        let response;
        if (this.isEditing) {
          response = await fetch(
            `/api/medicine/formulas/${this.formulaForm.id}`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(formData),
            }
          );
        } else {
          response = await fetch("/api/medicine/formulas", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(formData),
          });
        }

        const result = await response.json();

        if (result.success) {
          this.showMessage(
            this.isEditing ? "更新成功！" : "添加成功！",
            "success"
          );
          this.closeModal();
          await this.loadFormulas();
        } else {
          this.showMessage(
            (this.isEditing ? "更新失败: " : "添加失败: ") + result.message,
            "error"
          );
        }
      } catch (error) {
        console.error("保存药方错误:", error);
        this.showMessage("保存失败，请检查网络连接", "error");
      } finally {
        this.saving = false;
      }
    },

    // 显示导入弹窗
    openImportModal() {
      this.showImportModal = true;
      this.importFile = null;
    },

    // 关闭导入弹窗
    closeImportModal() {
      this.showImportModal = false;
      this.importFile = null;
    },

    // 处理文件选择
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        // 检查文件类型
        const allowedTypes = [
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.ms-excel",
        ];

        if (!allowedTypes.includes(file.type)) {
          this.showMessage("请选择Excel文件（.xlsx或.xls格式）", "error");
          return;
        }

        // 检查文件大小（10MB限制）
        if (file.size > 10 * 1024 * 1024) {
          this.showMessage("文件大小不能超过10MB", "error");
          return;
        }

        this.importFile = file;
      }
    },

    // 移除选择的文件
    removeFile() {
      this.importFile = null;
      if (this.$refs.fileInput) {
        this.$refs.fileInput.value = "";
      }
    },

    // 导入数据
    async importData() {
      if (!this.importFile) {
        this.showMessage("请选择要导入的文件", "error");
        return;
      }

      try {
        this.importing = true;

        const formData = new FormData();
        formData.append("file", this.importFile);

        const response = await fetch("/api/medicine/formulas/import", {
          method: "POST",
          body: formData,
        });

        const result = await response.json();

        if (result.success) {
          this.showMessage(result.message, "success");
          this.closeImportModal();
          await this.loadFormulas();

          // 显示详细结果
          if (result.results && result.results.errors.length > 0) {
            console.log("导入错误详情:", result.results.errors);
            alert(
              `导入完成！\n成功: ${result.results.success}条\n失败: ${result.results.failed}条\n\n错误详情请查看控制台`
            );
          }
        } else {
          this.showMessage("导入失败: " + result.message, "error");
        }
      } catch (error) {
        console.error("导入数据错误:", error);
        this.showMessage("导入失败，请检查网络连接", "error");
      } finally {
        this.importing = false;
      }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
  },
};
</script>

<style scoped>
/* 基础样式 */
.admin-medicine {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 20px;
  min-height: 600px;
}

/* 顶部控制栏 */
.control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 0;
  border-bottom: 2px solid rgba(102, 126, 234, 0.1);
}

.control-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.title-icon {
  font-size: 28px;
}

.stats-info {
  display: flex;
  gap: 15px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 12px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  font-size: 14px;
}

.stats-label {
  color: #666;
}

.stats-value {
  font-weight: 600;
  color: #667eea;
}

.control-right {
  display: flex;
  gap: 10px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.add-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.import-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.export-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 搜索栏 */
.search-bar {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.search-type-select {
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.search-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-btn,
.reset-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.search-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.reset-btn {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e5e9;
}

.search-btn:hover,
.reset-btn:hover {
  transform: translateY(-1px);
}

/* 表格样式 */
.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.table-wrapper {
  overflow-x: auto;
}

.formulas-table {
  width: 100%;
  border-collapse: collapse;
}

.formulas-table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 14px;
}

.formulas-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  vertical-align: top;
}

.formula-row:hover {
  background: rgba(102, 126, 234, 0.05);
}

.col-index {
  width: 60px;
  text-align: center;
}

.col-name {
  min-width: 200px;
}

.col-ingredients {
  min-width: 250px;
}

.col-references {
  min-width: 150px;
}

.col-notes {
  min-width: 150px;
}

.col-time {
  min-width: 120px;
}

.col-actions {
  width: 120px;
}

/* 表格内容样式 */
.name-cell {
  line-height: 1.4;
}

.formula-name {
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.alternative-names {
  font-size: 12px;
  color: #666;
}

.alt-label {
  font-weight: 500;
}

.alt-names {
  color: #999;
}

.ingredients-cell,
.references-cell,
.notes-cell {
  max-width: 200px;
}

.ingredients-text,
.references-text,
.notes-text {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #555;
  font-size: 14px;
}

.create-time {
  font-size: 12px;
  color: #666;
}

/* 操作按钮 */
.actions-cell {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.action-icon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
}

.detail-btn {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.edit-btn {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.delete-btn {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.action-icon-btn:hover {
  transform: scale(1.1);
}

/* 详情展开行 */
.detail-row {
  background: #f8f9fa;
}

.detail-cell {
  padding: 20px !important;
}

.detail-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.detail-section {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.detail-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.detail-text {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
}

/* 加载和空状态 */
.loading-cell,
.empty-cell {
  text-align: center;
  padding: 40px !important;
}

.loading-spinner,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.spinner-icon,
.empty-icon {
  font-size: 32px;
  opacity: 0.6;
}

.loading-text,
.empty-text {
  font-size: 16px;
  color: #666;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid #e1e5e9;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number-btn {
  width: 36px;
  height: 36px;
  border: 2px solid #e1e5e9;
  background: white;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.page-number-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
}

.page-number-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.page-number-btn:disabled {
  cursor: default;
  border: none;
  background: transparent;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .control-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .control-left,
  .control-right {
    justify-content: center;
  }

  .control-right {
    flex-wrap: wrap;
  }

  .search-input-group {
    flex-direction: column;
    gap: 10px;
  }

  .search-type-select,
  .search-input {
    width: 100%;
  }

  .table-wrapper {
    font-size: 12px;
  }

  .formulas-table th,
  .formulas-table td {
    padding: 8px 6px;
  }

  .action-icon-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 10px;
  }

  .page-numbers {
    flex-wrap: wrap;
    justify-content: center;
  }

  .detail-content {
    grid-template-columns: 1fr;
  }
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.modal-icon {
  font-size: 20px;
}

.modal-close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-icon {
  font-size: 16px;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.formula-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.required {
  color: #dc3545;
}

.form-input,
.form-textarea {
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  transition: border-color 0.3s;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-hint {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.modal-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e1e5e9;
}

.save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn:hover:not(:disabled) {
  transform: translateY(-1px);
}

.modal-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 弹窗移动端适配 */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: 16px;
  }

  .form-row {
    flex-direction: column;
    gap: 10px;
  }

  .modal-footer {
    flex-direction: column;
  }

  .modal-btn {
    width: 100%;
    justify-content: center;
  }
}

/* 导入弹窗特殊样式 */
.import-modal {
  max-width: 700px;
}

.import-instructions {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #667eea;
}

.import-instructions h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 16px;
}

.import-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.import-instructions li {
  margin-bottom: 5px;
  color: #555;
  font-size: 14px;
}

.import-area {
  margin-top: 20px;
}

.file-drop-zone {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  background: #fafafa;
}

.file-drop-zone:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.drop-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.drop-icon {
  font-size: 48px;
  opacity: 0.6;
}

.drop-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.drop-hint {
  font-size: 14px;
  color: #666;
}

.selected-file {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  margin-top: 15px;
}

.file-icon {
  font-size: 20px;
  color: #667eea;
}

.file-name {
  flex: 1;
  font-weight: 600;
  color: #333;
}

.file-size {
  font-size: 12px;
  color: #666;
}

.remove-file-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s;
}

.remove-file-btn:hover {
  background: rgba(220, 53, 69, 0.1);
}

.remove-icon {
  font-size: 14px;
  color: #dc3545;
}

.import-confirm-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.import-confirm-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
}
</style>
